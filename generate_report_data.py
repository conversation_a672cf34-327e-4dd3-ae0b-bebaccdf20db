#!/usr/bin/env python3
"""
Generate processed data for HTML report
"""

import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics

def process_traffic_patterns(data):
    """Process traffic patterns for visualization"""
    
    # Hourly traffic pattern (24-hour format)
    hourly_pattern = defaultdict(int)
    daily_totals = defaultdict(int)
    
    for hour_key, count in data['hourly_traffic'].items():
        try:
            dt = datetime.fromisoformat(hour_key.replace(' ', 'T') + ':00')
            hour = dt.hour
            day = dt.strftime('%Y-%m-%d')
            hourly_pattern[hour] += count
            daily_totals[day] += count
        except:
            continue
    
    # Convert to lists for Chart.js
    hourly_data = [hourly_pattern[i] for i in range(24)]
    
    # Daily traffic over time
    sorted_days = sorted(daily_totals.items())
    daily_labels = [day for day, _ in sorted_days]
    daily_data = [count for _, count in sorted_days]
    
    return {
        'hourly_pattern': {
            'labels': [f'{i:02d}:00' for i in range(24)],
            'data': hourly_data
        },
        'daily_traffic': {
            'labels': daily_labels,
            'data': daily_data
        }
    }

def process_security_analysis(data):
    """Process security-related data"""
    
    # Group error patterns by type
    error_types = Counter()
    for error in data['error_patterns']:
        for pattern in error['patterns']:
            error_types[pattern] += 1
    
    # Failed auth by IP
    failed_auth_ips = Counter()
    for auth in data['failed_auth']:
        failed_auth_ips[auth['ip']] += 1
    
    # Large requests by IP
    large_req_ips = Counter()
    for req in data['large_requests']:
        large_req_ips[req['ip']] += 1
    
    return {
        'error_types': dict(error_types),
        'failed_auth_ips': dict(failed_auth_ips.most_common(10)),
        'large_req_ips': dict(large_req_ips.most_common(10)),
        'total_security_events': len(data['error_patterns']) + len(data['failed_auth']) + len(data['large_requests'])
    }

def generate_geographic_data(data):
    """Generate geographic data based on IP ranges"""
    
    # Simple IP geolocation based on common ranges
    geo_data = {
        'internal': 0,
        'cloudflare': 0,
        'external': 0
    }
    
    for ip, count in data['ips'].items():
        if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
            geo_data['internal'] += count
        elif ip.startswith('172.68.') or ip.startswith('172.69.') or ip.startswith('162.158.'):
            geo_data['cloudflare'] += count
        else:
            geo_data['external'] += count
    
    return geo_data

def calculate_performance_metrics(data):
    """Calculate performance-related metrics"""
    
    if not data['response_times'] or not data['response_sizes']:
        return {}
    
    response_times = data['response_times']
    response_sizes = data['response_sizes']
    
    return {
        'response_time': {
            'avg': statistics.mean(response_times),
            'median': statistics.median(response_times),
            'p95': sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0,
            'p99': sorted(response_times)[int(len(response_times) * 0.99)] if response_times else 0
        },
        'response_size': {
            'avg': statistics.mean(response_sizes),
            'median': statistics.median(response_sizes),
            'total_gb': sum(response_sizes) / (1024**3)
        }
    }

def identify_anomalies(data):
    """Identify traffic anomalies and unusual patterns"""
    
    anomalies = []
    
    # Check for IPs with unusually high request rates
    avg_requests = data['stats']['avg_requests_per_ip']
    for ip, count in data['ips'].items():
        if count > avg_requests * 10:  # 10x average
            anomalies.append({
                'type': 'high_volume_ip',
                'ip': ip,
                'requests': count,
                'severity': 'high' if count > avg_requests * 50 else 'medium'
            })
    
    # Check for unusual status code patterns
    total_requests = data['total_requests']
    error_rate = (data['status_codes'].get('400', 0) + 
                  data['status_codes'].get('404', 0) + 
                  data['status_codes'].get('500', 0)) / total_requests
    
    if error_rate > 0.05:  # > 5% error rate
        anomalies.append({
            'type': 'high_error_rate',
            'rate': error_rate * 100,
            'severity': 'high' if error_rate > 0.1 else 'medium'
        })
    
    # Check for potential bot traffic
    bot_indicators = 0
    for url in data['urls']:
        if any(keyword in url.lower() for keyword in ['bot', 'crawl', 'spider', 'robots']):
            bot_indicators += data['urls'][url]
    
    if bot_indicators > total_requests * 0.01:  # > 1% bot traffic
        anomalies.append({
            'type': 'bot_traffic',
            'requests': bot_indicators,
            'percentage': (bot_indicators / total_requests) * 100,
            'severity': 'low'
        })
    
    return anomalies

def main():
    print("Loading analysis data...")
    with open('log_analysis.json', 'r') as f:
        data = json.load(f)
    
    print("Processing traffic patterns...")
    traffic_patterns = process_traffic_patterns(data)
    
    print("Analyzing security data...")
    security_analysis = process_security_analysis(data)
    
    print("Generating geographic data...")
    geo_data = generate_geographic_data(data)
    
    print("Calculating performance metrics...")
    performance_metrics = calculate_performance_metrics(data)
    
    print("Identifying anomalies...")
    anomalies = identify_anomalies(data)
    
    # Prepare chart data
    chart_data = {
        'status_codes': {
            'labels': list(data['status_codes'].keys()),
            'data': list(data['status_codes'].values())
        },
        'top_ips': {
            'labels': list(data['ips'].keys())[:10],
            'data': list(data['ips'].values())[:10]
        },
        'top_urls': {
            'labels': [url[:50] + '...' if len(url) > 50 else url for url in list(data['urls'].keys())[:10]],
            'data': list(data['urls'].values())[:10]
        },
        'geographic': {
            'labels': list(geo_data.keys()),
            'data': list(geo_data.values())
        }
    }
    
    # Combine all processed data
    report_data = {
        'summary': {
            'total_requests': data['total_requests'],
            'unique_ips': len(data['unique_ips']),
            'date_range': data['date_range'],
            'avg_requests_per_ip': data['stats']['avg_requests_per_ip'],
            'error_rate': data['stats']['error_rate'],
            'peak_hour': data['stats']['peak_hour'],
            'peak_day': data['stats']['peak_day']
        },
        'traffic_patterns': traffic_patterns,
        'security_analysis': security_analysis,
        'performance_metrics': performance_metrics,
        'anomalies': anomalies,
        'chart_data': chart_data,
        'suspicious_ips': data['suspicious_ips'][:20],  # Top 20 suspicious IPs
        'recent_errors': data['error_patterns'][-50:] if data['error_patterns'] else [],  # Last 50 errors
        'failed_auth_recent': data['failed_auth'][-20:] if data['failed_auth'] else []  # Last 20 failed auth
    }
    
    # Save processed data
    with open('report_data.json', 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print("Report data generated successfully!")
    print(f"Total anomalies detected: {len(anomalies)}")
    print(f"Security events: {security_analysis['total_security_events']}")

if __name__ == "__main__":
    main()
