#!/usr/bin/env python3
"""
Analyze 500 errors in detail
"""

import re
import gzip
import json
import os
from datetime import datetime
from collections import defaultdict, Counter

def analyze_500_errors():
    """Extract and analyze all 500 errors from log files"""
    
    # Apache Common Log Format pattern
    log_pattern = re.compile(
        r'(\S+) - - \[([^\]]+)\] "(\S+) ([^"]*) (\S+)" (\d+) (\S+) (\d+)'
    )
    
    error_500_data = {
        'total_500_errors': 0,
        'errors_by_url': Counter(),
        'errors_by_ip': Counter(),
        'errors_by_hour': defaultdict(int),
        'errors_by_day': defaultdict(int),
        'recent_500_errors': [],
        'top_error_urls': [],
        'top_error_ips': []
    }
    
    def parse_timestamp(timestamp_str):
        """Parse Apache timestamp format"""
        try:
            dt = datetime.strptime(timestamp_str.split()[0], '%d/%b/%Y:%H:%M:%S')
            return dt
        except ValueError:
            return None
    
    def process_log_file(filepath):
        """Process a single log file for 500 errors"""
        print(f"Analyzing 500 errors in {filepath}...")
        
        if filepath.endswith('.gz'):
            opener = gzip.open
            mode = 'rt'
        else:
            opener = open
            mode = 'r'
        
        try:
            with opener(filepath, mode, encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    match = log_pattern.match(line.strip())
                    if not match:
                        continue
                    
                    ip, timestamp_str, method, url, protocol, status, size, response_time = match.groups()
                    
                    # Only process 500 errors
                    if status == '500':
                        timestamp = parse_timestamp(timestamp_str)
                        if not timestamp:
                            continue
                        
                        error_500_data['total_500_errors'] += 1
                        error_500_data['errors_by_url'][url] += 1
                        error_500_data['errors_by_ip'][ip] += 1
                        
                        # Time-based analysis
                        hour_key = timestamp.strftime('%Y-%m-%d %H:00')
                        day_key = timestamp.strftime('%Y-%m-%d')
                        error_500_data['errors_by_hour'][hour_key] += 1
                        error_500_data['errors_by_day'][day_key] += 1
                        
                        # Store recent errors (keep last 100)
                        error_entry = {
                            'timestamp': timestamp.isoformat(),
                            'ip': ip,
                            'method': method,
                            'url': url,
                            'response_time': int(response_time) if response_time.isdigit() else 0,
                            'size': size
                        }
                        
                        error_500_data['recent_500_errors'].append(error_entry)
                        
                        # Keep only the most recent 100 errors
                        if len(error_500_data['recent_500_errors']) > 100:
                            error_500_data['recent_500_errors'] = error_500_data['recent_500_errors'][-100:]
                    
                    if line_num % 100000 == 0:
                        print(f"  Processed {line_num:,} lines, found {error_500_data['total_500_errors']} 500 errors so far...")
        
        except Exception as e:
            print(f"Error processing {filepath}: {e}")
    
    # Get all log files
    log_files = []
    for filename in os.listdir('.'):
        if filename.startswith('access.log'):
            log_files.append(filename)
    
    # Sort files to process in chronological order
    log_files.sort()
    
    # Process all log files
    for log_file in log_files:
        process_log_file(log_file)
    
    # Generate top lists
    error_500_data['top_error_urls'] = [
        {'url': url, 'count': count} 
        for url, count in error_500_data['errors_by_url'].most_common(20)
    ]
    
    error_500_data['top_error_ips'] = [
        {'ip': ip, 'count': count} 
        for ip, count in error_500_data['errors_by_ip'].most_common(20)
    ]
    
    # Convert defaultdicts to regular dicts for JSON serialization
    error_500_data['errors_by_hour'] = dict(error_500_data['errors_by_hour'])
    error_500_data['errors_by_day'] = dict(error_500_data['errors_by_day'])
    error_500_data['errors_by_url'] = dict(error_500_data['errors_by_url'])
    error_500_data['errors_by_ip'] = dict(error_500_data['errors_by_ip'])
    
    # Sort recent errors by timestamp (most recent first)
    error_500_data['recent_500_errors'].sort(key=lambda x: x['timestamp'], reverse=True)
    
    return error_500_data

if __name__ == "__main__":
    print("Analyzing 500 errors...")
    error_data = analyze_500_errors()
    
    # Save the 500 error analysis
    with open('500_errors_analysis.json', 'w') as f:
        json.dump(error_data, f, indent=2)
    
    print(f"\n=== 500 ERROR ANALYSIS COMPLETE ===")
    print(f"Total 500 errors found: {error_data['total_500_errors']:,}")
    print(f"Unique URLs with 500 errors: {len(error_data['errors_by_url'])}")
    print(f"Unique IPs generating 500 errors: {len(error_data['errors_by_ip'])}")
    
    print(f"\n=== TOP 10 URLs WITH 500 ERRORS ===")
    for i, error in enumerate(error_data['top_error_urls'][:10], 1):
        url_display = error['url'][:80] + '...' if len(error['url']) > 80 else error['url']
        print(f"{i:2d}. {error['count']:3d} errors: {url_display}")
    
    print(f"\n=== TOP 10 IPs GENERATING 500 ERRORS ===")
    for i, error in enumerate(error_data['top_error_ips'][:10], 1):
        print(f"{i:2d}. {error['count']:3d} errors: {error['ip']}")
    
    print(f"\nResults saved to 500_errors_analysis.json")
