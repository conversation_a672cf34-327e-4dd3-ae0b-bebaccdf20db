{"summary": {"total_requests": 2965180, "unique_ips": 7058, "date_range": {"start": "2025-07-16T00:00:19", "end": "2025-07-30T14:31:46"}, "avg_requests_per_ip": 420.1161802210258, "error_rate": 0.00033724765444256334, "peak_hour": ["2025-07-22 09:00", 26959], "peak_day": ["2025-07-22", 283926]}, "traffic_patterns": {"hourly_pattern": {"labels": ["00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"], "data": [46595, 50849, 45923, 43947, 43690, 60394, 62102, 113824, 240704, 269734, 273668, 258554, 211782, 247878, 250176, 227921, 145192, 68047, 56014, 54938, 51165, 48456, 48549, 45078]}, "daily_traffic": {"labels": ["2025-07-16", "2025-07-17", "2025-07-18", "2025-07-19", "2025-07-20", "2025-07-21", "2025-07-22", "2025-07-23", "2025-07-24", "2025-07-25", "2025-07-26", "2025-07-27", "2025-07-28", "2025-07-29", "2025-07-30"], "data": [252252, 236010, 207717, 66503, 63018, 274473, 283926, 251995, 251083, 227111, 88549, 78097, 266149, 247431, 170866]}}, "security_analysis": {"error_types": {"sql_injection": 19381, "path_traversal": 538, "xss_attempt": 110}, "failed_auth_ips": {"127.0.0.1": 1064, "*************": 269, "***************": 2, "***************": 2, "***************": 2, "nessus.imtins.com": 2, "**************": 2, "************": 1, "**************": 1, "***********": 1}, "large_req_ips": {"***************": 108, "***************": 84, "**************": 84, "**************": 82, "***************": 80, "***************": 74, "***************": 73, "***************": 72, "*************5": 66, "***************": 62}, "total_security_events": 25805}, "performance_metrics": {"response_time": {"avg": 1.2345125759650342, "median": 0.0, "p95": 4, "p99": 16}, "response_size": {"avg": 20635.26155668907, "median": 37.0, "total_gb": 51.01402206066996}}, "anomalies": [{"type": "high_volume_ip", "ip": "*************", "requests": 24359, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7606, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21384, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************", "requests": 16260, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 37950, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 6348, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 85686, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************", "requests": 13078, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 12963, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5667, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5200, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 21866, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 20340, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 19353, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************0", "requests": 22056, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 13427, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 11292, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 20285, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9954, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************1", "requests": 16640, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 17301, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 19415, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************7", "requests": 16106, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21440, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 19670, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 19069, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 18585, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 18979, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21323, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21033, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 19924, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21232, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 17819, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21145, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************", "requests": 17557, "severity": "medium"}, {"type": "high_volume_ip", "ip": "************", "requests": 4245, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21586, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 20873, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 20293, "severity": "medium"}, {"type": "high_volume_ip", "ip": "127.0.0.1", "requests": 37694, "severity": "high"}, {"type": "high_volume_ip", "ip": "*************04", "requests": 5866, "severity": "medium"}, {"type": "high_volume_ip", "ip": "************", "requests": 4431, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************2", "requests": 133075, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9025, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 6992, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8032, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7226, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************08", "requests": 5868, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 21570, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 6222, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5702, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6162, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 11581, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9786, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9374, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5609, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5622, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************6", "requests": 11009, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5083, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 22092, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8518, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6049, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************0", "requests": 5705, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8324, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4429, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************9", "requests": 7278, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************6", "requests": 11194, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6443, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7246, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7190, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 10998, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************3", "requests": 5800, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7184, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************5", "requests": 8811, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 30135, "severity": "high"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8586, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7447, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8667, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************7", "requests": 6484, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 15504, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8754, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************9", "requests": 8628, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.231.142", "requests": 6595, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.80", "requests": 7050, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************0", "requests": 14946, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.231.39", "requests": 5558, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.49", "requests": 4600, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.85", "requests": 5837, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7829, "severity": "medium"}, {"type": "high_volume_ip", "ip": "192.168.153.22", "requests": 7291, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8239, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7828, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7168, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 6845, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4537, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 15529, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4467, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5405, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 19588, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5073, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 14867, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5019, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 6856, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 18996, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9406, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8786, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4985, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************3", "requests": 10819, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 17422, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 12647, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************2", "requests": 7825, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 4561, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************8", "requests": 9583, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4284, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7766, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 15182, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 10186, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 19653, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 17214, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 18349, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 55430, "severity": "high"}, {"type": "high_volume_ip", "ip": "***************", "requests": 10678, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 11006, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5390, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8776, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5204, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 7112, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8016, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8826, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8433, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4798, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 5354, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8376, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9582, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 9931, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 14701, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 11197, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************7", "requests": 12102, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4320, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4818, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 9518, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4253, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 6333, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4580, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8337, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5703, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 8408, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7067, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 10311, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 8476, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 12387, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7070, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 9871, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 9067, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 11402, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 7124, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 4278, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 5440, "severity": "medium"}, {"type": "high_volume_ip", "ip": "**************", "requests": 13928, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 4817, "severity": "medium"}, {"type": "high_volume_ip", "ip": "*************", "requests": 7493, "severity": "medium"}, {"type": "high_volume_ip", "ip": "***************", "requests": 7531, "severity": "medium"}], "chart_data": {"status_codes": {"labels": ["200", "302", "400", "301", "404", "500", "304", "401", "201", "206", "416", "504", "403", "408", "405", "501"], "data": [2508012, 345515, 21523, 50085, 27770, 925, 9101, 1068, 359, 6, 4, 491, 305, 13, 2, 1]}, "top_ips": {"labels": ["*************", "***************", "***************", "**************", "***************", "***************", "*************", "**************", "***************", "***************"], "data": [24359, 7606, 21384, 16260, 37950, 6348, 85686, 13078, 12963, 5667]}, "top_urls": {"labels": ["/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl...", "/getfile.pl?rootdirectory=/cgilogs/Claims/&file=cl..."], "data": [1, 1, 1, 1, 1, 1, 114384, 1, 1, 1]}, "geographic": {"labels": ["internal", "cloudflare", "external"], "data": [2638889, 179214, 147077]}}, "suspicious_ips": [{"ip": "**************2", "request_count": 133075, "reason": "High request volume"}, {"ip": "*************", "request_count": 85686, "reason": "High request volume"}, {"ip": "***************", "request_count": 55430, "reason": "High request volume"}, {"ip": "***************", "request_count": 37950, "reason": "High request volume"}, {"ip": "127.0.0.1", "request_count": 37694, "reason": "High request volume"}, {"ip": "***************", "request_count": 30135, "reason": "High request volume"}, {"ip": "*************", "request_count": 24359, "reason": "High request volume"}, {"ip": "***************", "request_count": 22092, "reason": "High request volume"}, {"ip": "**************0", "request_count": 22056, "reason": "High request volume"}, {"ip": "**************", "request_count": 21866, "reason": "High request volume"}, {"ip": "***************", "request_count": 21586, "reason": "High request volume"}, {"ip": "***************", "request_count": 21570, "reason": "High request volume"}, {"ip": "***************", "request_count": 21440, "reason": "High request volume"}, {"ip": "***************", "request_count": 21384, "reason": "High request volume"}, {"ip": "***************", "request_count": 21323, "reason": "High request volume"}, {"ip": "***************", "request_count": 21232, "reason": "High request volume"}, {"ip": "***************", "request_count": 21145, "reason": "High request volume"}, {"ip": "***************", "request_count": 21033, "reason": "High request volume"}, {"ip": "***************", "request_count": 20873, "reason": "High request volume"}, {"ip": "*************", "request_count": 20340, "reason": "High request volume"}], "recent_errors": [{"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T19:56:47", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T19:57:51", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T19:59:47", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:01:33", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:04:27", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:06:16", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:10:11", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:10:31", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/WadenaPAS/wa_xrf_vehicleDelete.pl", "timestamp": "2025-07-21T20:11:01", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/WadenaPAS/wa_xrf_driverDelete.pl", "timestamp": "2025-07-21T20:11:06", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:11:11", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:11:48", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:14:02", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:14:20", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:14:39", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:21:05", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:24:20", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:24:32", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:24:47", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:38:25", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:39:06", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:39:49", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:39:56", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:42:45", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:42:56", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T20:44:27", "patterns": ["sql_injection"], "status": "200"}, {"ip": "***********", "url": "/imtonline/Claims/Claims_Doc_AWS_Download.pl?key=AWS-bd40de75-fc34-4cfe-a80a-ab5c4e704d35;filename=4.16.25%202024R0156%20Sommer%20transcript%20for%20Penny%20Zacek.msg", "timestamp": "2025-07-21T20:57:33", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/css/dropzones.min.css?v=1.5.2", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/alertify.min.css", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/themes/bootstrap.min.css", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/dropzone.min.js?v=1.0.6", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/alertify.min.js", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/ckalert.min.js?v=1.0.1", "timestamp": "2025-07-21T22:26:35", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/Claims/claim_scripts_Monetary.js", "timestamp": "2025-07-21T22:27:34", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:30:56", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:32:50", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:33:25", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:34:16", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:46:46", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T22:55:18", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/css/dropzones.min.css?v=1.5.2", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/alertify.min.css", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/css/themes/bootstrap.min.css", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/dropzone.min.js?v=1.0.6", "timestamp": "2025-07-21T23:00:54", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/alertifyjs/alertify.min.js", "timestamp": "2025-07-21T23:00:55", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/js/ckalert.min.js?v=1.0.1", "timestamp": "2025-07-21T23:00:55", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T23:25:06", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/Claims/claim_scripts_Monetary.js", "timestamp": "2025-07-21T23:25:14", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T23:41:24", "patterns": ["sql_injection"], "status": "200"}, {"ip": "**************", "url": "/imtonline/document_management/claims/api/v1/ui/refresh/select/", "timestamp": "2025-07-21T23:55:24", "patterns": ["sql_injection"], "status": "200"}], "failed_auth_recent": [{"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:54", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:55", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:57", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:57", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:58", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:58", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:51:59", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T16:52:12", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:15:08", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:30:09", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:30:09", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:35:03", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T17:35:04", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:29:46", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:29:46", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:59:53", "status": "401"}, {"ip": "127.0.0.1", "url": "/api/v1/platform/users/get_auth_token.pl", "timestamp": "2025-07-21T18:59:54", "status": "401"}]}